import React, { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import usePromptStore from '@/hooks/usePromptStore';
import PromptCard from '@/components/PromptCard';
import PromptEditor from '@/components/PromptEditor';
import Sidebar from '@/components/Sidebar';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface Prompt {
  id: string;
  title: string;
  content: string;
  tags: string[];
}

interface EditingPrompt {
  id: string;
  title: string;
  content: string;
  tags: string;
}

const Index: React.FC = () => {
  const { prompts, addPrompt, updatePrompt, deletePrompt } = usePromptStore();
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<EditingPrompt | null>(null);
  const [promptToDelete, setPromptToDelete] = useState<string | null>(null);

  const handleNewPrompt = () => {
    setEditingPrompt(null);
    setIsEditorOpen(true);
  };

  const handleEditPrompt = (id: string) => {
    const prompt = prompts.find((p: Prompt) => p.id === id);
    if (prompt) {
      setEditingPrompt({ ...prompt, tags: prompt.tags.join(', ') });
      setIsEditorOpen(true);
    }
  };

  const handleDeletePrompt = (id: string) => {
    setPromptToDelete(id);
  };

  const confirmDelete = () => {
    if (promptToDelete) {
      deletePrompt(promptToDelete);
      setPromptToDelete(null);
    }
  };

  const handleEditorSubmit = (data: { title: string; content: string; tags?: string }) => {
    const tagsArray = data.tags ? data.tags.split(',').map((tag: string) => tag.trim()) : [];
    if (editingPrompt) {
      updatePrompt({ ...editingPrompt, title: data.title, content: data.content, tags: tagsArray });
    } else {
      addPrompt({ id: uuidv4(), title: data.title, content: data.content, tags: tagsArray });
    }
    setIsEditorOpen(false);
  };

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 p-6 overflow-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">My Prompts</h1>
          <Button onClick={handleNewPrompt}>Create New Prompt</Button>
        </div>
        <Separator className="mb-6" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {prompts.map((prompt: Prompt) => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              onEdit={handleEditPrompt}
              onDelete={handleDeletePrompt}
            />
          ))}
        </div>

        <PromptEditor
          isOpen={isEditorOpen}
          onClose={() => setIsEditorOpen(false)}
          onSubmit={handleEditorSubmit}
          initialData={editingPrompt || undefined}
        />

        <AlertDialog open={!!promptToDelete} onOpenChange={() => setPromptToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete your prompt.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={confirmDelete}>Continue</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default Index;