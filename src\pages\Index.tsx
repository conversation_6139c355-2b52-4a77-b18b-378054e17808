import React, { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import usePromptStore from '@/hooks/usePromptStore';
import PromptCard from '@/components/PromptCard';
import PromptEditor from '@/components/PromptEditor';
import Sidebar from '@/components/Sidebar';
import { Button } from '@/components/ui/button';

import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

interface Prompt {
  id: string;
  title: string;
  content: string;
  tags: string[];
}

interface EditingPrompt {
  id: string;
  title: string;
  content: string;
  tags: string;
}

const Index: React.FC = () => {
  const { prompts, addPrompt, updatePrompt, deletePrompt } = usePromptStore();
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<EditingPrompt | null>(null);
  const [promptToDelete, setPromptToDelete] = useState<string | null>(null);

  const handleNewPrompt = () => {
    setEditingPrompt(null);
    setIsEditorOpen(true);
  };

  const handleEditPrompt = (id: string) => {
    const prompt = prompts.find((p: Prompt) => p.id === id);
    if (prompt) {
      setEditingPrompt({ ...prompt, tags: prompt.tags.join(', ') });
      setIsEditorOpen(true);
    }
  };

  const handleDeletePrompt = (id: string) => {
    setPromptToDelete(id);
  };

  const confirmDelete = () => {
    if (promptToDelete) {
      deletePrompt(promptToDelete);
      setPromptToDelete(null);
    }
  };

  const handleEditorSubmit = (data: { title: string; content: string; tags?: string }) => {
    const tagsArray = data.tags ? data.tags.split(',').map((tag: string) => tag.trim()) : [];
    if (editingPrompt) {
      updatePrompt({ ...editingPrompt, title: data.title, content: data.content, tags: tagsArray });
    } else {
      addPrompt({ id: uuidv4(), title: data.title, content: data.content, tags: tagsArray });
    }
    setIsEditorOpen(false);
  };

  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50">
          <div className="flex h-16 items-center justify-between px-6">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold tracking-tight">Prompt Palace</h1>
              <div className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
                <span>•</span>
                <span>{prompts.length} prompts</span>
              </div>
            </div>
            <Button onClick={handleNewPrompt} className="shadow-sm">
              <span className="hidden sm:inline">Create New Prompt</span>
              <span className="sm:hidden">New</span>
            </Button>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            {prompts.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-[60vh] text-center">
                <div className="rounded-full bg-muted p-6 mb-4">
                  <svg className="h-12 w-12 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold mb-2">No prompts yet</h3>
                <p className="text-muted-foreground mb-6 max-w-sm">
                  Get started by creating your first prompt. Organize your AI prompts and access them easily.
                </p>
                <Button onClick={handleNewPrompt} size="lg">
                  Create Your First Prompt
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {prompts.map((prompt: Prompt) => (
                  <PromptCard
                    key={prompt.id}
                    prompt={prompt}
                    onEdit={handleEditPrompt}
                    onDelete={handleDeletePrompt}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      <PromptEditor
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSubmit={handleEditorSubmit}
        initialData={editingPrompt || undefined}
      />

      <AlertDialog open={!!promptToDelete} onOpenChange={() => setPromptToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your prompt.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>Continue</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Index;