import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface PromptCardProps {
  prompt: {
    id: string;
    title: string;
    content: string;
    tags: string[];
  };
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

const PromptCard: React.FC<PromptCardProps> = ({ prompt, onEdit, onDelete }) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{prompt.title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-500 line-clamp-3">{prompt.content}</p>
        <div className="flex flex-wrap gap-2 mt-2">
          {prompt.tags.map((tag) => (
            <span key={tag} className="text-xs bg-gray-200 px-2 py-1 rounded-full">
              {tag}
            </span>
          ))}
        </div>
        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" size="sm" onClick={() => onEdit(prompt.id)}>Edit</Button>
          <Button variant="destructive" size="sm" onClick={() => onDelete(prompt.id)}>Delete</Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PromptCard;
