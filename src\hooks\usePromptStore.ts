import { create } from 'zustand';

interface Prompt {
  id: string;
  title: string;
  content: string;
  tags: string[];
}

interface PromptStore {
  prompts: Prompt[];
  addPrompt: (prompt: Prompt) => void;
  updatePrompt: (prompt: Prompt) => void;
  deletePrompt: (id: string) => void;
}

const usePromptStore = create<PromptStore>((set) => ({
  prompts: [],
  addPrompt: (prompt) => set((state) => ({ prompts: [...state.prompts, prompt] })),
  updatePrompt: (updatedPrompt) =>
    set((state) => ({
      prompts: state.prompts.map((prompt) =>
        prompt.id === updatedPrompt.id ? updatedPrompt : prompt
      ),
    })),
  deletePrompt: (id) =>
    set((state) => ({
      prompts: state.prompts.filter((prompt) => prompt.id !== id),
    })),
}));

export default usePromptStore;
