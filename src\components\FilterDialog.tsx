import React from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';

interface FilterDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const FilterDialog: React.FC<FilterDialogProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Filter Prompts</DialogTitle>
        </DialogHeader>
        {/* Add filter options here */}
        <p>Filter options will go here.</p>
      </DialogContent>
    </Dialog>
  );
};

export default FilterDialog;
